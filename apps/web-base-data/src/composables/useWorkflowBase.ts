import type { WorkflowOptions } from '@vben/base-ui';

import { useWorkflow } from '@vben/base-ui';

import {
  getApprovalDetailApi,
  getProcessDefinitionApi,
  getProcessDefinitionListByFormKeyApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
} from '#/api';

export function useWorkflowBase(options?: WorkflowOptions) {
  return useWorkflow(
    {
      getProcessDefinitionApi,
      getApprovalDetailApi,
      getProcessDefinitionListByFormKeyApi,
      getUserInfoByIdsApi,
      getUserListByKeywordApi,
      getUserTreeListApi,
    },
    options,
  );
}
