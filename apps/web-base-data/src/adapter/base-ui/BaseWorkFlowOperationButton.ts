import { defineComponent } from 'vue';

import { WorkflowOperationButton } from '@vben/base-ui';

import * as taskApi from '#/api';

export const BaseWorkflowOperationButton = defineComponent({
  ...WorkflowOperationButton,
  props: {
    ...(WorkflowOperationButton as any).props,
    taskApi: {
      type: Object,
      ...(WorkflowOperationButton as any).props.api,
      required: false,
      default: taskApi,
    },
  },
});
