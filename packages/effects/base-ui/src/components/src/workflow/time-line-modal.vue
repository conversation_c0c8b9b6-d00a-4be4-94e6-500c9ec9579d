<script setup lang="ts">
import type { BpmProcessInstanceApi } from '#/components/src/workflow/type';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { WorkflowTimeLine } from '#/components';

const activityNodes = ref<BpmProcessInstanceApi.ApprovalNodeInfo[]>([]);
let api = {};
const [Modal, modalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData<Record<string, any>>();
      activityNodes.value = data.activityNodes;
      api = data.api;
    }
  },
});
const handleUserSelectConfirm = (activityId: number, userList: any[]) => {
  modalApi.setData({ activityId, userList });
};
defineExpose({ modalApi });
</script>

<template>
  <Modal title="确认流程节点">
    <WorkflowTimeLine :activity-nodes="activityNodes" :api="api" @select-user-confirm="handleUserSelectConfirm" />
  </Modal>
</template>

<style></style>
