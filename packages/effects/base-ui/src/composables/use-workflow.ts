import type { Ref } from 'vue';

import type { BpmModelApi, BpmProcessInstanceApi } from '#/components/src/workflow/type';

import { computed, nextTick, ref } from 'vue';

import { confirm, prompt, useVbenModal } from '@vben/common-ui';

import { message, Select } from 'ant-design-vue';

import { BpmCandidateStrategyEnum } from '#/components/src/workflow/constants';
import TimeLineModal from '#/components/src/workflow/time-line-modal.vue';
import OperationButton from '#/components/src/workflow/operation-button.vue';

// 导出 OperationButton 组件供外部使用
export { OperationButton };

export interface WorkflowConfig {
  key?: string;
  processInstanceId?: string;
  processDefinitionId?: string;
  formKey?: string;
}

// export interface WorkflowOptions {
//   operationButtonRef?: Ref<any>;
// }

export interface WorkflowApiConfig {
  getProcessDefinitionApi: (params: { id?: string; key?: string }) => Promise<any>;
  getApprovalDetailApi: (params: {
    [key: string]: any;
    processDefinitionId?: string;
    processInstanceId?: string;
  }) => Promise<any>;
  getProcessDefinitionListByFormKeyApi: (params: { formKey: string }) => Promise<any>;
  getUserInfoByIdsApi: (ids: string[]) => Promise<any>;
  getUserListByKeywordApi: (params: { keyword: string }) => Promise<any>;
  getUserTreeListApi: (params: { name: string; orgId: string }) => Promise<any>;
}

export function useWorkflow(apiConfig: WorkflowApiConfig, options?: WorkflowOptions) {
  const config = ref<WorkflowConfig>({});
  const processDefinition = ref<any>({});
  const approvalDetail = ref<any>({});
  const processInstance = ref<any>({});
  const processDefinitionList = ref<BpmModelApi.ProcessDefinition[]>([]);
  const isWorkflow = computed(() => config.value.key && config.value.processInstanceId);

  let currentPromiseResolve: ((value: any) => void) | null = null;
  let currentPromiseReject: ((reason?: any) => void) | null = null;
  const [WorkflowTimeLineModal, timeLineModalApi] = useVbenModal({
    connectedComponent: TimeLineModal,
    destroyOnClose: true,
    onConfirm: async () => {
      const data = timeLineModalApi.getData();
      // 获取发起人自选的任务
      const startUserSelectTasks =
        approvalDetail.value.activityNodes?.filter(
          (node: BpmProcessInstanceApi.ApprovalNodeInfo) =>
            BpmCandidateStrategyEnum.START_USER_SELECT === node.candidateStrategy,
        ) || [];
      const startUserSelectAssignees: Record<string, string[]> = {};
      if (startUserSelectTasks?.length > 0) {
        if (!data.activityId || !Array.isArray(data.userList)) {
          message.warning('请选择候选人');
          return false;
        }
        startUserSelectAssignees[data.activityId] = data.userList.map((item) => item.id);
        for (const userTask of startUserSelectTasks) {
          const assignees = startUserSelectAssignees[userTask.id];
          if (Array.isArray(assignees) && assignees.length === 0) {
            message.warning(`请选择${userTask.name}的候选人`);
            return false;
          }
        }
      }
      if (currentPromiseResolve) {
        currentPromiseResolve(startUserSelectAssignees);
      }
      await timeLineModalApi.close();
      currentPromiseResolve = null;
      currentPromiseReject = null;
      await timeLineModalApi.close();
    },
    onClosed: () => {
      if (currentPromiseReject) {
        currentPromiseReject(new Error('取消选择'));
      }
      currentPromiseResolve = null;
      currentPromiseReject = null;
    },
  });
  const getProcessDefinitionDetail = async () => {
    if (!config.value.key) return;
    try {
      processDefinition.value = await apiConfig.getProcessDefinitionApi({ key: config.value.key });
    } catch (error) {
      console.error('获取流程定义失败', error);
    }
  };
  const getApprovalDetail = async () => {
    if (!config.value.processInstanceId && !config.value.processDefinitionId) return;
    approvalDetail.value = await apiConfig.getApprovalDetailApi({
      processInstanceId: config.value.processInstanceId,
      processDefinitionId: config.value.processDefinitionId,
    });
    processInstance.value = approvalDetail.value.processInstance;
  };
  const getProcessDefinitionList = async () => {
    if (!config.value.formKey) return;
    processDefinitionList.value = await apiConfig.getProcessDefinitionListByFormKeyApi({
      formKey: config.value.formKey,
    });
  };
  const initWorkflow = async (data?: WorkflowConfig) => {
    config.value = Object.assign(config.value, data);
    await getProcessDefinitionDetail();
    await getApprovalDetail();
    if (options?.operationButtonRef) {
      await nextTick();
      options.operationButtonRef.value?.loadTodoTask(approvalDetail.value.todoTask);
    }
  };
  const startInitWorkflow = async (data: { formKey: string }) => {
    config.value.formKey = data.formKey;
    await getProcessDefinitionList();
  };
  const startWorkflow = async () => {
    let key: string | undefined;
    let processDefinition: BpmModelApi.ProcessDefinition | undefined;
    if (processDefinitionList.value.length === 0) {
      await confirm('未匹配到可用的审批流程，提交将立即生效，是否确认？', '提示');
    } else if (processDefinitionList.value.length === 1 && processDefinitionList.value[0]) {
      key = processDefinitionList.value[0].key;
      config.value.key = key;
      processDefinition = processDefinitionList.value[0];
      config.value.processDefinitionId = processDefinition?.id;
    } else {
      key = await prompt({
        component: Select,
        componentProps: {
          options: processDefinitionList.value,
          placeholder: '请选择',
          popupClassName: 'pointer-events-auto',
          fieldNames: { label: 'name', value: 'key' },
        },
        content: '',
        title: '选择审批流程',
        modelPropName: 'value',
      });
      config.value.key = key;
      processDefinition = processDefinitionList.value.find((item: any) => item.key === key);
      config.value.processDefinitionId = processDefinition?.id;
    }
    await getApprovalDetail();
    const startUserSelectAssignees = await openTimeLineModal();
    return { processDefinitionKey: key, startUserSelectAssignees };
  };

  const openTimeLineModal = async (api?: any) => {
    if (currentPromiseResolve) {
      console.error('Another timeline modal is already open and waiting for resolution.');
      throw new Error('Modal conflict: A modal is already open.');
    }
    if (!approvalDetail.value?.activityNodes) {
      console.warn('没有可用的审批节点数据');
      return;
    }
    return new Promise((resolve, reject) => {
      currentPromiseResolve = resolve;
      currentPromiseReject = reject;
      timeLineModalApi
        .setData({
          activityNodes: approvalDetail.value.activityNodes,
          api: api || apiConfig,
        })
        .open();
    });
  };

  return {
    initWorkflow,
    startInitWorkflow,
    startWorkflow,
    openTimeLineModal,
    isWorkflow,
    processDefinition,
    processInstance,
    approvalDetail,
    WorkflowTimeLineModal,
    timeLineModalApi,
    OperationButton,
  };
}
